package personal.george;

import java.time.LocalDate;

/**
 * @description: ${description}
 * @author: <PERSON>
 * @date: 2025-04-10
 **/
public class Main {
    public static void main(String[] args) {
        StatementDownloader downloader = new DinStatementDownloader();
        // 从2025-05-01遍历到2025-05-31
        LocalDate startDate = LocalDate.of(2025, 5, 1);
        LocalDate endDate = LocalDate.of(2025, 5, 2);
        while (!startDate.isAfter(endDate)) {
            String date = startDate.toString().replace("-", "");
            downloader.download(date);
            startDate = startDate.plusDays(1);
        }
    }
}